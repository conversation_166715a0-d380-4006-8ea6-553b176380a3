# Admin User Setup

This document explains how to create an admin user for the PrintNet 3D printing vending machine platform using database migrations.

## Overview

The admin user is created automatically through a database migration (`CreateAdminUser1748880633378`) that:

- ✅ Creates an admin user with secure password hashing (bcrypt)
- ✅ Assigns both CLIENT and ADMIN roles
- ✅ Checks for existing users to prevent duplicates
- ✅ Uses environment variables for customization
- ✅ Provides rollback capability
- ✅ Includes comprehensive logging

## Quick Start

### Default Admin User

If no environment variables are set, the migration will create:

- **Email**: `<EMAIL>`
- **Password**: `AdminPassword123!`
- **Name**: `Admin User`
- **Roles**: `client`, `admin`

### Running the Migration

```bash
# Using Docker Compose (recommended)
docker-compose exec api npm run migration:run

# Or locally
npm run migration:run
```

## Customization

You can customize the admin user by setting environment variables before running the migration:

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `ADMIN_EMAIL` | `<EMAIL>` | Admin user email address |
| `ADMIN_PASSWORD` | `AdminPassword123!` | Admin user password |
| `ADMIN_FIRST_NAME` | `Admin` | Admin user first name |
| `ADMIN_LAST_NAME` | `User` | Admin user last name |

### Example with Custom Values

#### Option 1: Using .env file

Add to your `.env` file:
```env
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=YourSecurePassword123!
ADMIN_FIRST_NAME=John
ADMIN_LAST_NAME=Doe
```

#### Option 2: Using Docker Compose environment

Update your `docker-compose.yml`:
```yaml
services:
  api:
    environment:
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=YourSecurePassword123!
      - ADMIN_FIRST_NAME=John
      - ADMIN_LAST_NAME=Doe
```

#### Option 3: Inline environment variables

```bash
ADMIN_EMAIL=<EMAIL> ADMIN_PASSWORD=SecurePass123! docker-compose exec api npm run migration:run
```

## Security Considerations

### Password Requirements

- Minimum 8 characters (recommended: 12+ characters)
- Include uppercase and lowercase letters
- Include numbers and special characters
- Avoid common passwords or dictionary words

### Best Practices

1. **Change default credentials immediately** in production
2. **Use strong, unique passwords**
3. **Store credentials securely** (environment variables, secrets management)
4. **Rotate passwords regularly**
5. **Monitor admin account usage**

## Migration Details

### What the Migration Does

1. **Checks for existing user** with the specified email
2. **Hashes the password** using bcrypt with 10 salt rounds
3. **Creates the user record** in the `users` table
4. **Assigns CLIENT role** (default for all users)
5. **Assigns ADMIN role** (for admin privileges)
6. **Logs success information** (without exposing password)

### Database Tables Affected

- `users` - Main user record
- `user_roles` - Role assignments (client + admin)

### Rollback Support

The migration includes a `down()` method that:
- Removes the admin user and associated roles
- Uses the same email from environment variables
- Provides confirmation logging

```bash
# Rollback the migration
npm run migration:revert
```

## Troubleshooting

### Common Issues

**"Admin user already exists"**
- The migration skips creation if a user with the specified email exists
- This is normal and safe behavior
- Check existing users or use a different email

**"Password too weak"**
- Ensure your password meets security requirements
- Use a mix of characters, numbers, and symbols

**"Migration fails to run"**
- Check database connection
- Verify environment variables are set correctly
- Check database permissions

### Verification

After running the migration, verify the admin user was created:

```sql
-- Check if user exists
SELECT id, email, first_name, last_name, is_active, created_at 
FROM users 
WHERE email = '<EMAIL>';

-- Check user roles
SELECT u.email, ur.role 
FROM users u 
JOIN user_roles ur ON u.id = ur.user_id 
WHERE u.email = '<EMAIL>';
```

### Login Testing

Test the admin login through your application:

1. Navigate to the login page
2. Use the admin email and password
3. Verify admin dashboard access
4. Check admin-only features are available

## Production Deployment

### Recommended Workflow

1. **Set environment variables** with secure credentials
2. **Run database migrations** as part of deployment
3. **Verify admin user creation** in logs
4. **Test admin login** functionality
5. **Document credentials** securely for your team

### CI/CD Integration

```bash
# Example deployment script
export ADMIN_EMAIL="<EMAIL>"
export ADMIN_PASSWORD="$(generate_secure_password)"

# Run migrations
docker-compose exec api npm run migration:run

# Verify deployment
docker-compose exec api npm run migration:show
```

## Support

If you encounter issues:

1. Check the migration logs for detailed error messages
2. Verify database connectivity and permissions
3. Ensure all environment variables are properly set
4. Review the migration code for any customizations needed

For additional help, refer to the main project documentation or contact the development team.
