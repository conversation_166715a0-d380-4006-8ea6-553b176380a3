import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AdminController } from "./controllers/admin.controller";
import { AdminService } from "./services/admin.service";
import { VendingMachine } from "../devices/entities/vending-machine.entity";
import { Order } from "../orders/entities/order.entity";
import { User } from "../users/entities/user.entity";
import { MonitoringModule } from "../monitoring/monitoring.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([VendingMachine, Order, User]),
    MonitoringModule,
  ],
  controllers: [AdminController],
  providers: [AdminService],
  exports: [AdminService],
})
export class AdminModule {}
