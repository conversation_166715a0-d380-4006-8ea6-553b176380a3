import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { MonitoringService } from "./services/monitoring.service";
import { MonitoringController } from "./controllers/monitoring.controller";
import { VendingMachine } from "../devices/entities/vending-machine.entity";
import { DeviceStatus } from "../devices/entities/device-status.entity";
import { MaintenanceModule } from "../maintenance/maintenance.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([VendingMachine, DeviceStatus]),
    EventEmitterModule.forRoot(),
    MaintenanceModule,
  ],
  controllers: [MonitoringController],
  providers: [MonitoringService],
  exports: [MonitoringService],
})
export class MonitoringModule {}
