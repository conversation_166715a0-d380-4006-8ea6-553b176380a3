import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { MaintenanceRequest } from "../../devices/entities/maintenance-request.entity";
import { VendingMachine } from "../../devices/entities/vending-machine.entity";

@Injectable()
export class MaintenanceService {
  constructor(
    @InjectRepository(MaintenanceRequest)
    private maintenanceRequestRepository: Repository<MaintenanceRequest>,
    @InjectRepository(VendingMachine)
    private vendingMachineRepository: Repository<VendingMachine>,
  ) {}

  async createMaintenance(param: {
    machineId: string;
    requestDate: Date;
    description?: string;
  }) {
    const machine = await this.vendingMachineRepository.findOne({
      where: { id: param.machineId },
    });
    if (!machine) {
      throw new NotFoundException(
        `Vending machine with ID ${param.machineId} not found`,
      );
    }

    const maintenanceRequest = this.maintenanceRequestRepository.create({
      machine,
      requestDate: param.requestDate,
    });

    await this.maintenanceRequestRepository.save(maintenanceRequest);
    return maintenanceRequest;
  }

  async updateMaintenance(
    id: string,
    updateData: Partial<{ requestDate: Date; description?: string }>,
  ) {
    const maintenanceRequest = await this.maintenanceRequestRepository.findOne({
      where: { id },
    });
    if (!maintenanceRequest) {
      throw new NotFoundException(
        `Maintenance request with ID ${id} not found`,
      );
    }

    Object.assign(maintenanceRequest, updateData);
    await this.maintenanceRequestRepository.save(maintenanceRequest);
    return maintenanceRequest;
  }

  async deleteMaintenance(id: string) {
    const maintenanceRequest = await this.maintenanceRequestRepository.findOne({
      where: { id },
    });
    if (!maintenanceRequest) {
      throw new NotFoundException(
        `Maintenance request with ID ${id} not found`,
      );
    }
    await this.maintenanceRequestRepository.remove(maintenanceRequest);
    return { message: "Maintenance request deleted successfully" };
  }

  async findAll() {
    return this.maintenanceRequestRepository.find({
      relations: ["machine"],
      order: { requestDate: "DESC" },
    });
  }

  async findOne(id: string) {
    const maintenanceRequest = await this.maintenanceRequestRepository.findOne({
      where: { id },
      relations: ["machine"],
    });

    if (!maintenanceRequest) {
      throw new NotFoundException(
        `Maintenance request with ID ${id} not found`,
      );
    }

    return maintenanceRequest;
  }
}
