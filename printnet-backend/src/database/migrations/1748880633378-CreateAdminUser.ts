import { MigrationInterface, QueryRunner } from "typeorm";
import * as bcrypt from "bcrypt";

export class CreateAdminUser1748880633378 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Default admin credentials
    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
    const adminPassword = process.env.ADMIN_PASSWORD || "AdminPassword123!";
    const adminFirstName = process.env.ADMIN_FIRST_NAME || "Admin";
    const adminLastName = process.env.ADMIN_LAST_NAME || "User";

    // Hash the password
    const hashedPassword = await bcrypt.hash(adminPassword, 10);

    // Check if admin user already exists
    const existingUser = await queryRunner.query(
      `SELECT id FROM users WHERE email = $1`,
      [adminEmail],
    );

    if (existingUser.length > 0) {
      console.log(
        `Admin user with email ${adminEmail} already exists. Skipping creation.`,
      );
      return;
    }

    // Insert admin user
    const userResult = await queryRunner.query(
      `INSERT INTO users (email, password_hash, first_name, last_name, is_active)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING id`,
      [adminEmail, hashedPassword, adminFirstName, adminLastName, true],
    );

    const userId = userResult[0].id;

    // Assign CLIENT role (default)
    await queryRunner.query(
      `INSERT INTO user_roles (role, user_id) VALUES ($1, $2)`,
      ["client", userId],
    );

    // Assign ADMIN role
    await queryRunner.query(
      `INSERT INTO user_roles (role, user_id) VALUES ($1, $2)`,
      ["admin", userId],
    );

    console.log(`✅ Admin user created successfully:`);
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Name: ${adminFirstName} ${adminLastName}`);
    console.log(`   Roles: client, admin`);
    console.log(`   Password: [HIDDEN]`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove admin user and associated roles
    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";

    // Get user ID
    const userResult = await queryRunner.query(
      `SELECT id FROM users WHERE email = $1`,
      [adminEmail],
    );

    if (userResult.length === 0) {
      console.log(
        `Admin user with email ${adminEmail} not found. Nothing to remove.`,
      );
      return;
    }

    const userId = userResult[0].id;

    // Remove user roles (will cascade due to foreign key)
    await queryRunner.query(`DELETE FROM user_roles WHERE user_id = $1`, [
      userId,
    ]);

    // Remove user
    await queryRunner.query(`DELETE FROM users WHERE id = $1`, [userId]);

    console.log(`🗑️ Admin user with email ${adminEmail} removed successfully.`);
  }
}
