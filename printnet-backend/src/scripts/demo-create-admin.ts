#!/usr/bin/env ts-node

/**
 * Demo script showing how to use the AdminCreationScript programmatically
 *
 * This is useful for:
 * - Automated deployments
 * - Testing environments
 * - Development setup
 *
 * Usage: npm run script:demo-create-admin
 */

import { AdminCreationScript } from "./create-admin";

async function createDemoAdmin() {
  console.log("🎭 Demo: Creating admin user programmatically\n");

  const script = new AdminCreationScript();

  try {
    // Initialize the script
    await script.initialize();

    // Demo data - in real scenarios, this would come from environment variables
    // or secure configuration
    const demoAdminData = {
      email: "<EMAIL>",
      password: "DemoPassword123!",
      firstName: "Demo",
      lastName: "Administrator",
      phone: "+380123456789",
    };

    console.log("📋 Demo admin data:");
    console.log(`   Email: ${demoAdminData.email}`);
    console.log(
      `   Name: ${demoAdminData.firstName} ${demoAdminData.lastName}`,
    );
    console.log(`   Phone: ${demoAdminData.phone}`);
    console.log("   Password: [HIDDEN]\n");

    // Create the admin user
    await (script as any).createAdminUser(demoAdminData);

    console.log("\n✅ Demo completed successfully!");
    console.log("💡 You can now login with the created admin credentials.");
  } catch (error) {
    console.error("\n❌ Demo failed:", error.message);

    if (error.message.includes("already exists")) {
      console.log(
        "\n💡 Tip: The demo admin user already exists. This is normal if you've run this demo before.",
      );
      console.log("   You can still use the existing credentials to login.");
    }
  } finally {
    // Clean up
    if ((script as any).app) {
      await (script as any).app.close();
    }
    console.log("\n👋 Demo script completed.");
  }
}

// Run the demo
if (require.main === module) {
  createDemoAdmin().catch((error) => {
    console.error("Unhandled demo error:", error);
    process.exit(1);
  });
}

export { createDemoAdmin };
