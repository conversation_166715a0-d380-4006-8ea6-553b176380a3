#!/usr/bin/env ts-node

import { NestFactory } from "@nestjs/core";
import { AppModule } from "../app.module";
import { UsersService } from "../modules/users/services/users.service";
import { UserRoleEnum } from "../modules/users/entities/user-role.entity";
import { CreateUserDto } from "../modules/users/dto/create-user.dto";
import * as readline from "readline";
import { ConflictException } from "@nestjs/common";

interface AdminUserData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
}

class AdminCreationScript {
  private app: any;
  private usersService: UsersService;
  private rl: readline.Interface;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async initialize(): Promise<void> {
    console.log("🚀 Initializing PrintNet Admin Creation Script...\n");

    try {
      this.app = await NestFactory.createApplicationContext(AppModule, {
        logger: false,
      });
      this.usersService = this.app.get(UsersService);
      console.log("✅ Application context initialized successfully\n");
    } catch (error) {
      console.error(
        "❌ Failed to initialize application context:",
        error.message,
      );
      process.exit(1);
    }
  }

  private question(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  private async getAdminData(): Promise<AdminUserData> {
    console.log("📝 Please provide admin user details:\n");

    const email = await this.question("Email address: ");
    if (!email || !this.isValidEmail(email)) {
      throw new Error("Invalid email address provided");
    }

    const password = await this.question("Password (min 8 characters): ");
    if (!password || password.length < 8) {
      throw new Error("Password must be at least 8 characters long");
    }

    const firstName = await this.question("First name (optional): ");
    const lastName = await this.question("Last name (optional): ");
    const phone = await this.question("Phone number (optional): ");

    return {
      email: email.trim(),
      password: password.trim(),
      firstName: firstName.trim() || undefined,
      lastName: lastName.trim() || undefined,
      phone: phone.trim() || undefined,
    };
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private async createAdminUser(userData: AdminUserData): Promise<void> {
    console.log("\n🔄 Creating admin user...");

    try {
      // Check if user already exists
      const existingUser = await this.usersService.findByEmail(userData.email);
      if (existingUser) {
        throw new ConflictException("User with this email already exists");
      }

      // Create user with CLIENT role first (default behavior)
      const createUserDto: CreateUserDto = {
        email: userData.email,
        password: userData.password,
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone,
      };

      const user = await this.usersService.create(createUserDto);
      console.log("✅ User created successfully");

      // Assign ADMIN role
      await this.usersService.assignRole(user.id, UserRoleEnum.ADMIN);
      console.log("✅ Admin role assigned successfully");

      // Fetch the complete user with roles
      const adminUser = await this.usersService.findOne(user.id);

      console.log("\n🎉 Admin user created successfully!");
      console.log("📋 User Details:");
      console.log(`   ID: ${adminUser.id}`);
      console.log(`   Email: ${adminUser.email}`);
      console.log(
        `   Name: ${adminUser.first_name || "N/A"} ${adminUser.last_name || "N/A"}`,
      );
      console.log(`   Phone: ${adminUser.phone || "N/A"}`);
      console.log(
        `   Roles: ${adminUser.roles.map((role) => role.role).join(", ")}`,
      );
      console.log(`   Created: ${adminUser.created_at}`);
      console.log(`   Active: ${adminUser.is_active ? "Yes" : "No"}`);
    } catch (error) {
      if (error instanceof ConflictException) {
        console.error("❌ Error: User with this email already exists");
      } else {
        console.error("❌ Error creating admin user:", error.message);
      }
      throw error;
    }
  }

  private parseCommandLineArgs(): Partial<AdminUserData> | null {
    const args = process.argv.slice(2);
    const userData: Partial<AdminUserData> = {};

    for (let i = 0; i < args.length; i += 2) {
      const flag = args[i];
      const value = args[i + 1];

      switch (flag) {
        case "--email":
          userData.email = value;
          break;
        case "--password":
          userData.password = value;
          break;
        case "--first-name":
          userData.firstName = value;
          break;
        case "--last-name":
          userData.lastName = value;
          break;
        case "--phone":
          userData.phone = value;
          break;
        case "--help":
          this.showHelp();
          return null;
        default:
          console.error(`Unknown flag: ${flag}`);
          this.showHelp();
          return null;
      }
    }

    return userData;
  }

  private showHelp(): void {
    console.log(`
📖 PrintNet Admin User Creation Script

Usage:
  npm run script:create-admin [options]

Options:
  --email <email>        Admin email address (required)
  --password <password>  Admin password (required, min 8 chars)
  --first-name <name>    Admin first name (optional)
  --last-name <name>     Admin last name (optional)
  --phone <phone>        Admin phone number (optional)
  --help                 Show this help message

Examples:
  # Interactive mode (recommended)
  npm run script:create-admin

  # Command line mode
  npm run script:create-admin --email <EMAIL> --password adminpass123 --first-name Admin --last-name User

  # Minimal command line mode
  npm run script:create-admin --email <EMAIL> --password adminpass123
`);
  }

  async run(): Promise<void> {
    try {
      const cmdArgs = this.parseCommandLineArgs();
      if (cmdArgs === null) {
        return; // Help was shown or error occurred
      }

      await this.initialize();

      let userData: AdminUserData;

      if (cmdArgs.email && cmdArgs.password) {
        // Command line mode
        console.log("🔧 Using command line arguments...\n");

        if (!this.isValidEmail(cmdArgs.email)) {
          throw new Error("Invalid email address provided");
        }

        if (cmdArgs.password.length < 8) {
          throw new Error("Password must be at least 8 characters long");
        }

        userData = {
          email: cmdArgs.email,
          password: cmdArgs.password,
          firstName: cmdArgs.firstName,
          lastName: cmdArgs.lastName,
          phone: cmdArgs.phone,
        };
      } else {
        // Interactive mode
        userData = await this.getAdminData();
      }

      await this.createAdminUser(userData);
    } catch (error) {
      console.error("\n💥 Script failed:", error.message);
      process.exit(1);
    } finally {
      this.rl.close();
      if (this.app) {
        await this.app.close();
      }
      console.log("\n👋 Script completed. Goodbye!");
    }
  }
}

// Run the script
if (require.main === module) {
  const script = new AdminCreationScript();
  script.run().catch((error) => {
    console.error("Unhandled error:", error);
    process.exit(1);
  });
}

export { AdminCreationScript };
