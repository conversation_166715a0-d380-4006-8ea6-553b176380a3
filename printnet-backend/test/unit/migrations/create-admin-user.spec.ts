import { CreateAdminUser1748880633378 } from '../../../src/database/migrations/1748880633378-CreateAdminUser';
import * as bcrypt from 'bcrypt';

describe('CreateAdminUser Migration', () => {
  let migration: CreateAdminUser1748880633378;
  let mockQueryRunner: any;

  beforeEach(() => {
    migration = new CreateAdminUser1748880633378();
    mockQueryRunner = {
      query: jest.fn(),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    // Reset environment variables
    delete process.env.ADMIN_EMAIL;
    delete process.env.ADMIN_PASSWORD;
    delete process.env.ADMIN_FIRST_NAME;
    delete process.env.ADMIN_LAST_NAME;
  });

  describe('up() method', () => {
    it('should create admin user with default values', async () => {
      // Mock no existing user
      mockQueryRunner.query.mockResolvedValueOnce([]);
      // Mock user creation
      mockQueryRunner.query.mockResolvedValueOnce([{ id: 'test-user-id' }]);
      // Mock role assignments
      mockQueryRunner.query.mockResolvedValueOnce(undefined);
      mockQueryRunner.query.mockResolvedValueOnce(undefined);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await migration.up(mockQueryRunner);

      // Verify user existence check
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        'SELECT id FROM users WHERE email = $1',
        ['<EMAIL>']
      );

      // Verify user creation
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO users'),
        expect.arrayContaining([
          '<EMAIL>',
          expect.any(String), // hashed password
          'Admin',
          'User',
          true
        ])
      );

      // Verify role assignments
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        'INSERT INTO user_roles (role, user_id) VALUES ($1, $2)',
        ['client', 'test-user-id']
      );

      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        'INSERT INTO user_roles (role, user_id) VALUES ($1, $2)',
        ['admin', 'test-user-id']
      );

      expect(consoleSpy).toHaveBeenCalledWith('✅ Admin user created successfully:');

      consoleSpy.mockRestore();
    });

    it('should use environment variables when provided', async () => {
      // Set environment variables
      process.env.ADMIN_EMAIL = '<EMAIL>';
      process.env.ADMIN_PASSWORD = 'CustomPassword123!';
      process.env.ADMIN_FIRST_NAME = 'Custom';
      process.env.ADMIN_LAST_NAME = 'Admin';

      // Mock no existing user
      mockQueryRunner.query.mockResolvedValueOnce([]);
      // Mock user creation
      mockQueryRunner.query.mockResolvedValueOnce([{ id: 'test-user-id' }]);
      // Mock role assignments
      mockQueryRunner.query.mockResolvedValueOnce(undefined);
      mockQueryRunner.query.mockResolvedValueOnce(undefined);

      await migration.up(mockQueryRunner);

      // Verify custom values were used
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        'SELECT id FROM users WHERE email = $1',
        ['<EMAIL>']
      );

      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO users'),
        expect.arrayContaining([
          '<EMAIL>',
          expect.any(String),
          'Custom',
          'Admin',
          true
        ])
      );
    });

    it('should skip creation if user already exists', async () => {
      // Mock existing user
      mockQueryRunner.query.mockResolvedValueOnce([{ id: 'existing-user-id' }]);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await migration.up(mockQueryRunner);

      // Should only check for existing user
      expect(mockQueryRunner.query).toHaveBeenCalledTimes(1);
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        'SELECT id FROM users WHERE email = $1',
        ['<EMAIL>']
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        'Admin user <NAME_EMAIL> already exists. Skipping creation.'
      );

      consoleSpy.mockRestore();
    });

    it('should hash password correctly', async () => {
      const testPassword = 'TestPassword123!';
      process.env.ADMIN_PASSWORD = testPassword;

      // Mock no existing user
      mockQueryRunner.query.mockResolvedValueOnce([]);
      // Mock user creation
      mockQueryRunner.query.mockResolvedValueOnce([{ id: 'test-user-id' }]);
      // Mock role assignments
      mockQueryRunner.query.mockResolvedValueOnce(undefined);
      mockQueryRunner.query.mockResolvedValueOnce(undefined);

      await migration.up(mockQueryRunner);

      // Get the hashed password from the call
      const userCreationCall = mockQueryRunner.query.mock.calls.find(call =>
        call[0].includes('INSERT INTO users')
      );
      const hashedPassword = userCreationCall[1][1];

      // Verify password was hashed correctly
      const isValidHash = await bcrypt.compare(testPassword, hashedPassword);
      expect(isValidHash).toBe(true);
    });
  });

  describe('down() method', () => {
    it('should remove admin user and roles', async () => {
      // Mock user exists
      mockQueryRunner.query.mockResolvedValueOnce([{ id: 'test-user-id' }]);
      // Mock role deletion
      mockQueryRunner.query.mockResolvedValueOnce(undefined);
      // Mock user deletion
      mockQueryRunner.query.mockResolvedValueOnce(undefined);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await migration.down(mockQueryRunner);

      // Verify user lookup
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        'SELECT id FROM users WHERE email = $1',
        ['<EMAIL>']
      );

      // Verify role deletion
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        'DELETE FROM user_roles WHERE user_id = $1',
        ['test-user-id']
      );

      // Verify user deletion
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        'DELETE FROM users WHERE id = $1',
        ['test-user-id']
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        '🗑️ Admin user <NAME_EMAIL> removed successfully.'
      );

      consoleSpy.mockRestore();
    });

    it('should skip removal if user does not exist', async () => {
      // Mock no user found
      mockQueryRunner.query.mockResolvedValueOnce([]);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await migration.down(mockQueryRunner);

      // Should only check for user existence
      expect(mockQueryRunner.query).toHaveBeenCalledTimes(1);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Admin user <NAME_EMAIL> not found. Nothing to remove.'
      );

      consoleSpy.mockRestore();
    });
  });
});
