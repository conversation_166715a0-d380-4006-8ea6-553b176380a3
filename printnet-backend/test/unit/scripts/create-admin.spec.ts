import { Test, TestingModule } from "@nestjs/testing";
import { AdminCreationScript } from "../../../src/scripts/create-admin";
import { UsersService } from "../../../src/modules/users/services/users.service";
import { UserRoleEnum } from "../../../src/modules/users/entities/user-role.entity";
import { ConflictException } from "@nestjs/common";
import { User } from "../../../src/modules/users/entities/user.entity";
import { beforeEach, describe } from "node:test";

describe("AdminCreationScript", () => {
  let script: AdminCreationScript;
  let usersService: any;

  const mockUser: User = {
    id: "test-user-id",
    email: "<EMAIL>",
    first_name: "Admin",
    last_name: "User",
    phone: "+380123456789",
    is_active: true,
    created_at: new Date(),
    updated_at: new Date(),
    password_hash: "hashed-password",
    roles: [
      {
        id: "role-1",
        role: UserRoleEnum.CLIENT,
        created_at: new Date(),
        user: {} as User,
      },
      {
        id: "role-2",
        role: UserRoleEnum.ADMIN,
        created_at: new Date(),
        user: {} as User,
      },
    ],
    orders: [],
    wallets: [],
  };

  beforeEach(async () => {
    const mockUsersService = {
      findByEmail: jest.fn(),
      create: jest.fn(),
      assignRole: jest.fn(),
      findOne: jest.fn(),
    };

    // Create a mock app context
    const mockApp = {
      get: jest.fn().mockReturnValue(mockUsersService),
      close: jest.fn(),
    };

    script = new AdminCreationScript();
    // Mock the app property
    (script as any).app = mockApp;
    (script as any).usersService = mockUsersService;

    usersService = mockUsersService;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Email validation", () => {
    it("should validate correct email addresses", () => {
      const validEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      validEmails.forEach((email) => {
        expect((script as any).isValidEmail(email)).toBe(true);
      });
    });

    it("should reject invalid email addresses", () => {
      const invalidEmails = [
        "invalid-email",
        "@domain.com",
        "user@",
        "user@domain",
        "",
        "user <EMAIL>",
      ];

      invalidEmails.forEach((email) => {
        expect((script as any).isValidEmail(email)).toBe(false);
      });
    });
  });

  describe("Command line argument parsing", () => {
    it("should parse valid command line arguments", () => {
      // Mock process.argv
      const originalArgv = process.argv;
      process.argv = [
        "node",
        "script.js",
        "--email",
        "<EMAIL>",
        "--password",
        "password123",
        "--first-name",
        "Admin",
        "--last-name",
        "User",
        "--phone",
        "+380123456789",
      ];

      const result = (script as any).parseCommandLineArgs();

      expect(result).toEqual({
        email: "<EMAIL>",
        password: "password123",
        firstName: "Admin",
        lastName: "User",
        phone: "+380123456789",
      });

      // Restore original argv
      process.argv = originalArgv;
    });

    it("should return null for help flag", () => {
      const originalArgv = process.argv;
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      process.argv = ["node", "script.js", "--help"];

      const result = (script as any).parseCommandLineArgs();

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalled();

      // Restore
      process.argv = originalArgv;
      consoleSpy.mockRestore();
    });

    it("should return null for unknown flags", () => {
      const originalArgv = process.argv;
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      process.argv = ["node", "script.js", "--unknown-flag", "value"];

      const result = (script as any).parseCommandLineArgs();

      expect(result).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Unknown flag: --unknown-flag",
      );

      // Restore
      process.argv = originalArgv;
      consoleErrorSpy.mockRestore();
      consoleSpy.mockRestore();
    });
  });

  describe("Admin user creation", () => {
    const userData = {
      email: "<EMAIL>",
      password: "password123",
      firstName: "Admin",
      lastName: "User",
      phone: "+380123456789",
    };

    it("should create admin user successfully", async () => {
      usersService.findByEmail.mockResolvedValue(null);
      usersService.create.mockResolvedValue(mockUser);
      usersService.assignRole.mockResolvedValue(undefined);
      usersService.findOne.mockResolvedValue(mockUser);

      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      await (script as any).createAdminUser(userData);

      expect(usersService.findByEmail).toHaveBeenCalledWith(userData.email);
      expect(usersService.create).toHaveBeenCalledWith({
        email: userData.email,
        password: userData.password,
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone,
      });
      expect(usersService.assignRole).toHaveBeenCalledWith(
        mockUser.id,
        UserRoleEnum.ADMIN,
      );
      expect(usersService.findOne).toHaveBeenCalledWith(mockUser.id);

      expect(consoleSpy).toHaveBeenCalledWith(
        "\n🎉 Admin user created successfully!",
      );

      consoleSpy.mockRestore();
    });

    it("should throw error if user already exists", async () => {
      usersService.findByEmail.mockResolvedValue(mockUser);

      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      await expect((script as any).createAdminUser(userData)).rejects.toThrow(
        ConflictException,
      );

      expect(usersService.findByEmail).toHaveBeenCalledWith(userData.email);
      expect(usersService.create).not.toHaveBeenCalled();
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "❌ Error: User with this email already exists",
      );

      consoleErrorSpy.mockRestore();
    });

    it("should handle creation errors gracefully", async () => {
      const error = new Error("Database connection failed");
      usersService.findByEmail.mockResolvedValue(null);
      usersService.create.mockRejectedValue(error);

      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      await expect((script as any).createAdminUser(userData)).rejects.toThrow(
        error,
      );

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "❌ Error creating admin user:",
        error.message,
      );

      consoleErrorSpy.mockRestore();
    });
  });
});
