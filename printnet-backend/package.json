{"name": "printnet-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --config ./test/jest-unit.json", "test:watch": "jest --config ./test/jest-unit.json --watch", "test:cov": "jest --config ./test/jest-unit.json --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --forceExit", "test:e2e:cov": "jest --config ./test/jest-e2e.json --coverage --forceExit", "test:all": "jest --config ./test/jest-unit.json && jest --config ./test/jest-e2e.json --forceExit", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm migration:generate -- -d src/database/data-source.ts", "migration:create": "npm run typeorm migration:create", "migration:run": "npm run build && npm run typeorm migration:run -- -d dist/database/data-source.js", "migration:revert": "npm run build && npm run typeorm migration:revert -- -d dist/database/data-source.js", "migration:show": "npm run typeorm migration:show -- -d src/database/data-source.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.0", "@nestjs/typeorm": "^10.0.2", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "mqtt": "^5.10.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger": "^0.7.5", "typeorm": "^0.3.20", "typeorm-naming-strategies": "^4.1.0"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/common": "^10.4.15", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.15", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^20.17.10", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}