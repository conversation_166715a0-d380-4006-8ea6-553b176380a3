; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-s3-devkitm-1]
platform = espressif32
board = esp32-s3-devkitm-1
framework = arduino

; Налаштування компілятора
build_type = debug

; Налаштування монітора
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Налаштування тестування
test_framework = unity
test_build_src = yes
test_filter = simple_test

; Бібліотеки
lib_deps =
    PubSubClient
    AsyncMqttClient
    WiFiManager
    Update
    WebServer
    FS
    DNSServer
    throwtheswitch/Unity@^2.5.2
    bblanchon/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@^6.19.4
    knolleary/PubSubClient@^2.8.0

; Налаштування прошивки
board_build.partitions = huge_app.csv
build_flags = 
    -D MQTT_MAX_PACKET_SIZE=1024
    -D MQTT_KEEPALIVE=60
    -D CORE_DEBUG_LEVEL=5
    -D CONFIG_ARDUHAL_LOG_COLORS=1

; Налаштування OTA
upload_protocol = espota
upload_port = 192.168.1.xxx  ; IP адреса пристрою
upload_flags =
    --port=3232
    --auth=your_password

; Налаштування Debug
debug_tool = esp-prog
debug_init_break = tbreak setup

[platformio]
default_envs = esp32-s3-devkitm-1

; доповнення для тестів
[env:test]
platform = espressif32
board = esp32dev
framework = arduino
test_framework = unity

build_flags = 
    -D UNITY_INCLUDE_CONFIG_H
    -D UNITY_OUTPUT_COLOR
    
lib_deps = 

    
test_build_src = yes
test_filter = test_*
monitor_speed = 115200

[env:native]
platform = native
test_framework = unity
build_flags = 
    -D UNITY_INCLUDE_CONFIG_H
    -D UNITY_OUTPUT_COLOR
    -D NATIVE_TEST
lib_deps = 
    throwtheswitch/Unity@^2.5.2
    bblanchon/ArduinoJson@^6.19.4
    