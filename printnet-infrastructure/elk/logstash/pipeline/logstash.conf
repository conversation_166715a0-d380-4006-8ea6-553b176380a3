input {
  beats {
    port => 5044
  }
  tcp {
    port => 5000
    codec => json
  }
}

filter {
  if [type] == "printnet-logs" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:log_level} %{GREEDYDATA:message}" }
    }
    date {
      match => [ "timestamp", "ISO8601" ]
      target => "@timestamp"
    }
  }

  if [type] == "vending-machine-metrics" {
    json {
      source => "message"
    }
    date {
      match => [ "timestamp", "ISO8601" ]
      target => "@timestamp"
    }
  }
}

output {
  if [type] == "printnet-logs" {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "printnet-logs-%{+YYYY.MM.dd}"
    }
  }

  if [type] == "vending-machine-metrics" {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "vending-metrics-%{+YYYY.MM.dd}"
    }
  }

  stdout {
    codec => rubydebug
  }
}