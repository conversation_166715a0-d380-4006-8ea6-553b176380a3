services:
  # Backend API
  api:
    build:
      context: ./printnet-backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - JWT_SECRET=${JWT_SECRET}
      - MQTT_URL=mqtt://mosquitto:1883
    depends_on:
      - postgres
      - mosquitto
    networks:
      - printnet-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # PostgreSQL
  postgres:
    image: postgres:17.1
    environment:
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_DATABASE}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - printnet-network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${DB_USERNAME}" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # MQTT Broker
  mosquitto:
    image: eclipse-mosquitto:2
    volumes:
      - ./printnet-infrastructure/message-broker/mosquitto/config:/mosquitto/config
      - mosquitto_data:/mosquitto/data
      - mosquitto_log:/mosquitto/log
    ports:
      - "1883:1883"
      - "9001:9001"
    networks:
      - printnet-network

  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./printnet-infrastructure/monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - "9090:9090"
    networks:
      - printnet-network

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.12.0
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - printnet-network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:9200" ]
      interval: 30s
      timeout: 10s
      retries: 5

  # Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.12.0
    volumes:
      - ./printnet-infrastructure/elk/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml
      - ./printnet-infrastructure/elk/logstash/pipeline:/usr/share/logstash/pipeline
    ports:
      - "5044:5044"
      - "5001:5000"
    environment:
      LS_JAVA_OPTS: "-Xmx256m -Xms256m"
    networks:
      - printnet-network
    depends_on:
      - elasticsearch

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.12.0
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - printnet-network
    depends_on:
      - elasticsearch

networks:
  printnet-network:
    driver: bridge

volumes:
  postgres_data:
  mosquitto_data:
  mosquitto_log:
  prometheus_data:
  elasticsearch_data:
