// User types
export interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  roles: UserRole[];
}

export interface UserRole {
  id: string;
  role: 'client' | 'admin';
  created_at: string;
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface AuthResponse {
  access_token: string;
  user: User;
}

// Order types
export interface Order {
  id: string;
  user: User;
  machine: VendingMachine;
  model_file_url: string;
  print_parameters: Record<string, any>;
  estimated_completion_time: string;
  actual_completion_time?: string;
  total_cost: number;
  created_at: string;
  updated_at: string;
  status: OrderStatus;
}

export interface OrderStatus {
  id: string;
  status: 'pending' | 'processing' | 'printing' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
}

export interface CreateOrderRequest {
  productId: string;
  quantity: number;
  address: string;
  machineId?: string;
}

// Vending Machine types
export interface VendingMachine {
  id: string;
  serial_number: string;
  location: string;
  is_active: boolean;
  maintenance_required: boolean;
  created_at: string;
  updated_at: string;
  printer_configs: PrinterConfig[];
  statuses: DeviceStatus[];
}

export interface PrinterConfig {
  id: string;
  machine_id: string;
  configuration: Record<string, any>;
}

export interface DeviceStatus {
  id: string;
  machine_id: string;
  status: 'active' | 'inactive' | 'maintenance' | 'error';
  telemetry: Record<string, any>;
  created_at: string;
}

// Payment types
export interface Payment {
  id: string;
  amount: number;
  currency: string;
  method: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Filter types
export interface OrderFilters {
  status?: string;
  startDate?: string;
  endDate?: string;
  userId?: string;
  machineId?: string;
}

export interface UserFilters {
  role?: string;
  isActive?: boolean;
  search?: string;
}

// Theme and Language types
export type Theme = 'light' | 'dark' | 'system';
export type Language = 'en' | 'ua';
