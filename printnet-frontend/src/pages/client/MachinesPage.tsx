import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search,
  Filter,
  MapPin,
  Printer,
  Thermometer,
  Droplets,
  Clock,
  CheckCircle,
  AlertTriangle,
  Plus
} from 'lucide-react';

export function MachinesPage() {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data - replace with real API calls
  const machines = [
    {
      id: 'VM-001',
      serialNumber: 'PN-2024-001',
      location: 'Main Street 123',
      address: '123 Main Street, Downtown',
      status: 'active',
      isActive: true,
      maintenanceRequired: false,
      currentJob: 'ORD-001',
      estimatedCompletion: '2024-01-15T14:30:00Z',
      telemetry: {
        temperature: 22,
        humidity: 45,
        filamentLevel: 85
      },
      printerModel: 'PrintBot Pro X1',
      capabilities: ['PLA', 'ABS', 'PETG'],
      buildVolume: '220x220x250mm',
      lastMaintenance: '2024-01-10T09:00:00Z'
    },
    {
      id: 'VM-002',
      serialNumber: 'PN-2024-002',
      location: 'University Campus',
      address: 'University Library, 2nd Floor',
      status: 'active',
      isActive: true,
      maintenanceRequired: false,
      currentJob: null,
      telemetry: {
        temperature: 21,
        humidity: 50,
        filamentLevel: 92
      },
      printerModel: 'PrintBot Pro X1',
      capabilities: ['PLA', 'ABS', 'PETG'],
      buildVolume: '220x220x250mm',
      lastMaintenance: '2024-01-08T14:30:00Z'
    },
    {
      id: 'VM-003',
      serialNumber: 'PN-2024-003',
      location: 'Shopping Mall',
      address: 'Central Mall, Food Court Level',
      status: 'maintenance',
      isActive: true,
      maintenanceRequired: true,
      currentJob: null,
      telemetry: {
        temperature: 25,
        humidity: 40,
        filamentLevel: 15
      },
      printerModel: 'PrintBot Pro X2',
      capabilities: ['PLA', 'ABS', 'PETG', 'TPU'],
      buildVolume: '300x300x400mm',
      lastMaintenance: '2023-12-15T10:00:00Z'
    },
    {
      id: 'VM-004',
      serialNumber: 'PN-2024-004',
      location: 'Coffee Shop',
      address: 'Bean There Café, 456 Oak Avenue',
      status: 'inactive',
      isActive: false,
      maintenanceRequired: false,
      currentJob: null,
      telemetry: {
        temperature: 20,
        humidity: 55,
        filamentLevel: 0
      },
      printerModel: 'PrintBot Pro X1',
      capabilities: ['PLA', 'ABS'],
      buildVolume: '220x220x250mm',
      lastMaintenance: '2024-01-05T16:00:00Z'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'inactive':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />;
      case 'maintenance':
        return <AlertTriangle className="h-4 w-4" />;
      case 'inactive':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const filteredMachines = machines.filter(machine => {
    const matchesSearch = machine.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         machine.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         machine.address.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || machine.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const availableMachines = machines.filter(m => m.status === 'active' && !m.currentJob);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('machines.title')}</h1>
          <p className="text-muted-foreground">
            Find and monitor 3D printing vending machines near you
          </p>
        </div>
        <div className="text-right">
          <div className="text-sm text-muted-foreground">Available Machines</div>
          <div className="text-2xl font-bold text-green-600">{availableMachines.length}</div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by location or machine ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">{t('machines.active')}</SelectItem>
                <SelectItem value="maintenance">{t('machines.maintenance')}</SelectItem>
                <SelectItem value="inactive">{t('machines.inactive')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Machines Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredMachines.map((machine) => (
          <Card key={machine.id} className={machine.status === 'active' && !machine.currentJob ? 'border-green-200 dark:border-green-800' : ''}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Printer className="h-5 w-5" />
                    {machine.id}
                  </CardTitle>
                  <CardDescription>{machine.serialNumber}</CardDescription>
                </div>
                <Badge className={getStatusColor(machine.status)}>
                  {getStatusIcon(machine.status)}
                  <span className="ml-1">{t(`machines.${machine.status}`)}</span>
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Location */}
              <div>
                <h4 className="font-medium mb-1">Location</h4>
                <div className="text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {machine.location}
                  </div>
                  <div className="ml-4 text-xs">{machine.address}</div>
                </div>
              </div>

              {/* Printer Info */}
              <div>
                <h4 className="font-medium mb-1">Printer Details</h4>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div>Model: {machine.printerModel}</div>
                  <div>Build Volume: {machine.buildVolume}</div>
                  <div>Materials: {machine.capabilities.join(', ')}</div>
                </div>
              </div>

              {/* Status Info */}
              <div>
                <h4 className="font-medium mb-1">Status</h4>
                <div className="text-sm text-muted-foreground space-y-1">
                  {machine.currentJob ? (
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      Printing: {machine.currentJob}
                    </div>
                  ) : (
                    <div className="text-green-600">Available for new orders</div>
                  )}
                  {machine.estimatedCompletion && (
                    <div className="text-xs">
                      Est. Complete: {new Date(machine.estimatedCompletion).toLocaleString()}
                    </div>
                  )}
                </div>
              </div>

              {/* Telemetry */}
              <div>
                <h4 className="font-medium mb-1">Environment</h4>
                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div className="text-center">
                    <Thermometer className="h-4 w-4 mx-auto mb-1" />
                    <div className="text-xs text-muted-foreground">Temp</div>
                    <div className="font-medium">{machine.telemetry.temperature}°C</div>
                  </div>
                  <div className="text-center">
                    <Droplets className="h-4 w-4 mx-auto mb-1" />
                    <div className="text-xs text-muted-foreground">Humidity</div>
                    <div className="font-medium">{machine.telemetry.humidity}%</div>
                  </div>
                  <div className="text-center">
                    <div className="h-4 w-4 mx-auto mb-1 bg-blue-500 rounded-full"></div>
                    <div className="text-xs text-muted-foreground">Filament</div>
                    <div className="font-medium">{machine.telemetry.filamentLevel}%</div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="pt-2">
                {machine.status === 'active' && !machine.currentJob ? (
                  <Button className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Order Here
                  </Button>
                ) : (
                  <Button variant="outline" className="w-full" disabled>
                    {machine.status === 'maintenance' ? 'Under Maintenance' : 
                     machine.currentJob ? 'Currently Printing' : 'Unavailable'}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredMachines.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <div className="text-muted-foreground">
              No machines found matching your criteria.
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
