import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Plus,
  Search,
  Filter,
  MapPin,
  Clock,
  DollarSign,
  FileText,
  Eye
} from 'lucide-react';

export function OrdersPage() {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data - replace with real API calls
  const orders = [
    {
      id: 'ORD-001',
      status: 'printing',
      createdAt: '2024-01-15T10:30:00Z',
      estimatedCompletion: '2024-01-15T14:30:00Z',
      machine: 'VM-001',
      location: 'Main Street 123',
      cost: 25.99,
      modelFile: 'phone_case.stl',
      printParameters: {
        material: 'PLA',
        infill: '20%',
        layerHeight: '0.2mm'
      }
    },
    {
      id: 'ORD-002',
      status: 'completed',
      createdAt: '2024-01-14T09:15:00Z',
      completedAt: '2024-01-14T11:45:00Z',
      machine: 'VM-002',
      location: 'University Campus',
      cost: 18.50,
      modelFile: 'keychain.stl',
      printParameters: {
        material: 'PLA',
        infill: '15%',
        layerHeight: '0.15mm'
      }
    },
    {
      id: 'ORD-003',
      status: 'pending',
      createdAt: '2024-01-14T16:20:00Z',
      machine: 'VM-003',
      location: 'Shopping Mall',
      cost: 32.75,
      modelFile: 'miniature.stl',
      printParameters: {
        material: 'PETG',
        infill: '25%',
        layerHeight: '0.1mm'
      }
    },
    {
      id: 'ORD-004',
      status: 'failed',
      createdAt: '2024-01-13T14:10:00Z',
      machine: 'VM-001',
      location: 'Main Street 123',
      cost: 15.25,
      modelFile: 'bracket.stl',
      printParameters: {
        material: 'ABS',
        infill: '30%',
        layerHeight: '0.2mm'
      }
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'printing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.modelFile.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('orders.title')}</h1>
          <p className="text-muted-foreground">
            Manage and track your 3D printing orders
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {t('orders.createOrder')}
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search orders..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">{t('orders.pending')}</SelectItem>
                <SelectItem value="printing">{t('orders.printing')}</SelectItem>
                <SelectItem value="completed">{t('orders.completed')}</SelectItem>
                <SelectItem value="failed">{t('orders.failed')}</SelectItem>
                <SelectItem value="cancelled">{t('orders.cancelled')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Orders List */}
      <div className="grid gap-4">
        {filteredOrders.map((order) => (
          <Card key={order.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {order.id}
                    <Badge className={getStatusColor(order.status)}>
                      {t(`orders.${order.status}`)}
                    </Badge>
                  </CardTitle>
                  <CardDescription className="flex items-center gap-4 mt-2">
                    <span className="flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      {order.modelFile}
                    </span>
                    <span className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {order.location} ({order.machine})
                    </span>
                  </CardDescription>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold">${order.cost}</div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(order.createdAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Print Parameters</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div>Material: {order.printParameters.material}</div>
                    <div>Infill: {order.printParameters.infill}</div>
                    <div>Layer Height: {order.printParameters.layerHeight}</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Timeline</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      Created: {new Date(order.createdAt).toLocaleString()}
                    </div>
                    {order.estimatedCompletion && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Est. Complete: {new Date(order.estimatedCompletion).toLocaleString()}
                      </div>
                    )}
                    {order.completedAt && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Completed: {new Date(order.completedAt).toLocaleString()}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-end items-end">
                  <div className="space-x-2">
                    <Button variant="outline" size="sm">
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </Button>
                    {order.status === 'pending' && (
                      <Button variant="destructive" size="sm">
                        Cancel
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredOrders.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <div className="text-muted-foreground">
              No orders found matching your criteria.
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
