import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/stores/authStore';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  Printer, 
  Clock, 
  CheckCircle, 
  Plus,
  MapPin,
  CreditCard
} from 'lucide-react';

export function ClientDashboard() {
  const { t } = useTranslation();
  const { user } = useAuthStore();

  // Mock data - replace with real API calls
  const stats = {
    totalOrders: 12,
    activeOrders: 3,
    completedOrders: 9,
    totalSpent: 245.50,
  };

  const recentOrders = [
    {
      id: '1',
      status: 'printing',
      createdAt: '2024-01-15T10:30:00Z',
      estimatedCompletion: '2024-01-15T14:30:00Z',
      machine: 'VM-001',
      location: 'Main Street 123',
      cost: 25.99,
    },
    {
      id: '2',
      status: 'completed',
      createdAt: '2024-01-14T09:15:00Z',
      completedAt: '2024-01-14T11:45:00Z',
      machine: 'VM-002',
      location: 'University Campus',
      cost: 18.50,
    },
    {
      id: '3',
      status: 'pending',
      createdAt: '2024-01-14T16:20:00Z',
      machine: 'VM-003',
      location: 'Shopping Mall',
      cost: 32.75,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'printing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {t('dashboard.welcome')}, {user?.first_name || user?.email}!
        </h1>
        <p className="text-muted-foreground">
          Here's what's happening with your 3D printing orders today.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.totalOrders')}
            </CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalOrders}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.activeOrders')}
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeOrders}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.completedOrders')}
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedOrders}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Spent
            </CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalSpent}</div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>{t('dashboard.quickActions')}</CardTitle>
          <CardDescription>
            Quick shortcuts to common actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              {t('dashboard.createOrder')}
            </Button>
            <Button variant="outline">
              <Printer className="mr-2 h-4 w-4" />
              {t('dashboard.viewMachines')}
            </Button>
            <Button variant="outline">
              <MapPin className="mr-2 h-4 w-4" />
              Find Locations
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Orders */}
      <Card>
        <CardHeader>
          <CardTitle>{t('dashboard.recentOrders')}</CardTitle>
          <CardDescription>
            Your latest 3D printing orders
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentOrders.map((order) => (
              <div
                key={order.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Order #{order.id}</span>
                    <Badge className={getStatusColor(order.status)}>
                      {t(`orders.${order.status}`)}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {order.location} ({order.machine})
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Created: {new Date(order.createdAt).toLocaleDateString()}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">${order.cost}</div>
                  {order.status === 'printing' && order.estimatedCompletion && (
                    <div className="text-sm text-muted-foreground">
                      Est: {new Date(order.estimatedCompletion).toLocaleTimeString()}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
